<template>
  <el-dialog
    title="以图搜索"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
    destroy-on-close
    custom-class="max-w-[400px]"
    @closed="handleClose"
  >
    <div class="image-search-dialog">
      <!-- 上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          :action="uploadUrl"
          :headers="headers"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :show-file-list="false"
          :accept="accept"
          :limit="1"
          drag
          class="upload-dragger"
        >
          <div v-if="!imageUrl" class="upload-content">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将图片拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip">
              支持 JPG、PNG、JPEG 格式，文件大小不超过 5MB
            </div>
          </div>

          <!-- 图片预览 -->
          <div v-else class="image-preview">
            <el-image
              :src="imageUrl"
              :preview-src-list="[imageUrl]"
              fit="contain"
              style="width: 100%; height: 200px;"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
            <div class="image-actions">
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="handleRemoveImage"
              >
                重新上传
              </el-button>
            </div>
          </div>
        </el-upload>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploading" class="upload-progress">
        <el-progress :percentage="uploadProgress" :show-text="false"></el-progress>
        <p>正在上传图片...</p>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        :disabled="!imageUrl || uploading"
        @click="handleConfirm"
      >
        确认搜索
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  name: 'ImageSearchDialog',
  data() {
    return {
      dialogVisible: false,
      imageUrl: '',
      imageInfo: null,
      uploading: false,
      uploadProgress: 0,
      accept: 'image/jpeg,image/jpg,image/png',
      uploadUrl: process.env.VUE_APP_BASE_API + '/system/AutoOsmotic/import',
      headers: {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  methods: {
    // 打开弹窗
    open() {
      this.dialogVisible = true
    },

    // 关闭弹窗
    close() {
      this.dialogVisible = false
    },

    // 上传前校验
    handleBeforeUpload(file) {
      console.log('file', file)
      // 校验文件类型
      const isImage = /^image\/(jpeg|jpg|png)$/i.test(file.type)
      if (!isImage) {
        this.$message.error('只能上传 JPG、PNG、JPEG 格式的图片!')
        return false
      }

      // 校验文件大小
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }

      this.uploading = true
      this.uploadProgress = 0
      return true
    },

    // 上传成功
    handleUploadSuccess(response, file) {
      this.uploading = false

      if (response.code === 200) {
        this.imageUrl = response.url
        this.imageInfo = {
          name: file.name,
          url: response.url,
          response: response
        }
        this.$message.success('图片上传成功')
      } else {
        this.$message.error(response.msg || '上传失败')
      }
    },

    // 上传失败
    handleUploadError(err) {
      this.uploading = false
      this.$message.error('上传失败，请重试')
      console.error('Upload error:', err)
    },

    // 移除图片
    handleRemoveImage() {
      this.imageUrl = ''
      this.imageInfo = null
    },

    // 取消
    handleCancel() {
      this.close()
    },

    // 确认搜索
    handleConfirm() {
      if (!this.imageInfo) {
        this.$message.warning('请先上传图片')
        return
      }

      // 触发搜索事件
      this.$emit('confirm', this.imageInfo)
      this.close()
    },

    // 弹窗关闭时重置数据
    handleClose() {
      this.imageUrl = ''
      this.imageInfo = null
      this.uploading = false
      this.uploadProgress = 0
    }
  }
}
</script>

<style lang="scss" scoped>
.image-search-dialog {
  ::v-deep.upload-section {
    margin-bottom: 20px;

    .el-upload {
      width: 100%;
      min-width: 100%;
    }

    .upload-dragger {
      width: 100%;
      min-width: 100%;

      ::v-deep .el-upload-dragger {
        width: 100%;
        min-width: 100%;
        height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
    }

    .upload-content {
      text-align: center;

      .el-icon-upload {
        font-size: 67px;
        color: #c0c4cc;
        margin-bottom: 16px;
      }

      .el-upload__text {
        color: #606266;
        font-size: 14px;
        margin-bottom: 8px;
      }

      .el-upload__tip {
        color: #909399;
        font-size: 12px;
      }
    }

    .image-preview {
      position: relative;

      .image-actions {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 4px;
        padding: 5px;

        .el-button {
          color: white;
          padding: 5px 8px;
        }
      }

      .image-slot {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        color: #909399;
        font-size: 30px;
      }
    }
  }

  .upload-progress {
    text-align: center;
    margin-bottom: 20px;

    p {
      margin-top: 10px;
      color: #606266;
      font-size: 14px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
