<template>
  <el-dialog title="权限分配" :visible.sync="visible" width="400px !important" append-to-body>
    <div class="algorithm-list-container">

      <el-form ref="form" :model="rowData1" label-width="80px">
        <el-form-item label="分级">
          <el-select v-model="rowData1.value9" multiple placeholder="请选择">
            <el-option v-for="(item, index) in classification" :key="index" :label="item.dictLabel" :value="item.dictValue">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="分类">
          <el-select v-model="rowData1.value10" multiple placeholder="请选择">
            <el-option v-for="(item, index) in category" :key="index" :label="item.dictLabel" :value="item.dictValue">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资源机构">
          <el-select v-model="rowData1.value11" multiple placeholder="请选择">
            <el-option v-for="(item, index) in resource" :key="index" :label="item.dictLabel" :value="item.dictValue">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request.js'

export default {
  name: 'GradeClass',

  data() {
    return {
      visible: false,
      rowData1: {
        value9: '',
        value10: '',
        value11: ''
      },
      rowData: {},
      classification: [],
      category: [],
      resource: []
    }
  },
  mounted() {
    Promise.all([
      this.getDicts('grades_lassified'),
      this.getDicts('classification'),
      this.getDicts('resource_agency')
    ]).then(([gradeResult, classResult, resourceResult]) => {
      this.classification = gradeResult.data
      this.category = classResult.data
      this.resource = resourceResult.data
      // 可以在这里添加后续需要执行的代码
      console.log(this.classification, 'classification data')
    })
  },
  methods: {
    handleOpen(row) {
      this.rowData = JSON.parse(JSON.stringify(row))
      this.visible = true
      try {
        this.rowData1.value9 = this.rowData.value9 ? this.rowData.value9?.split(',') : []
        this.rowData1.value10 = this.rowData.value10 ? this.rowData.value10?.split(',') : []
        this.rowData1.value11 = this.rowData.value11 ? this.rowData.value11?.split(',') : []
        console.log(this.rowData1, 'rowData1')
      } catch (e) {}
    },
    async confirm() {
      try {
        this.rowData.value9 = this.rowData1.value9.join(',')
        this.rowData.value10 = this.rowData1.value10.join(',')
        this.rowData.value11 = this.rowData1.value11.join(',')
        const response = await request({
          url: '/system/AutoOsmotic',
          method: 'PUT',
          data: {
            ...this.rowData
          }
        })
        if (response.code === 200) {
          this.$modal.msgSuccess('修改成功')
          this.$emit('success')
          this.visible = false
        }
      } catch (error) {
        console.error('修改失败:', error)
        this.$message.error('修改失败，请重试')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.algorithm-list-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
