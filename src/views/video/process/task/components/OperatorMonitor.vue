<template>
  <div class="operator-monitor">
    <EleTitle class="mb-5">运维监控列表</EleTitle>
    <EleSheet ref="sheetRef" v-bind="sheetProps" class="monitor-sheet page-main page-main--flat">
      <!-- 视频/图片预览列自定义渲染 -->
      <template #table:value4:simple="{ row }">
        <div v-if="row.value4" class="media-preview">
          <!-- 判断是视频还是图片 -->
          <video
            v-if="isVideo(row.value4)"
            :src="row.value4"
            controls
            class="preview-video"
            style="width: 80px !important; height: 50px !important;"
          />
          <el-image
            v-else
            :src="row.value4"
            alt=""
            class="preview-image"
            fit="cover"
            style="width: 80px !important; height: 50px !important;"
          />
        </div>
        <span v-else class="text-gray-400">暂无预览</span>
      </template>

      <!-- 进度列自定义渲染 -->
      <template #table:value5:simple="{ row }">
        <el-progress
          :percentage="Number(row.value5) || 0"
          :status="getProgressStatus(row.value6)"
          :stroke-width="8"
          :show-text="true"
        />
      </template>

      <!-- 日志内容列自定义渲染 -->
      <template #table:value8:simple="{ row }">
        <div v-if="row.value8" class="log-content">
          <el-tooltip :content="row.value8" placement="top">
            <span class="log-text">{{ truncateLog(row.value8) }}</span>
          </el-tooltip>
        </div>
        <span v-else class="text-gray-400">暂无日志</span>
      </template>
    </EleSheet>
  </div>
</template>

<script>
import request from '@/utils/request.js'
import { taskStatus } from '@/dicts/video/index'
export default {
  name: 'OperatorMonitor',
  props: {
    // 当前任务的ID
    taskId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      tableType: 'template_operator_progress'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '运维监控',
        layout: 'toolbar,table,paging',
        tableProps: {
          showIndex: true
        },
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType,
                value1: this.taskId
              }
            })
          // add: (data) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'post',
          //     data: {
          //       ...data,
          //       type: this.tableType,
          //       value1: this.taskId
          //     }
          //   })
        },
        hiddenActions: {
          // add: true,
          edit: true,
          remove: true,
          info: true
        },
        hiddenToolbar: {
          search: true,
          export: true,
          import: true
        },
        model: {
          value1: {
            hidden: true,
            value: this.taskId
          },
          value3: {
            type: 'text',
            label: '算子名称',
            showOverflowTooltip: true,
            search: { hidden: true }
          },
          value4: {
            type: 'text',
            label: '视频/图片预览',
            search: { hidden: true }
          },
          value5: {
            type: 'text',
            label: '进度',
            search: { hidden: true },
            align: 'left'
          },
          value6: {
            type: 'select',
            label: '状态',
            search: { hidden: true },
            options: taskStatus
          },
          value7: {
            type: 'text',
            label: '耗时',
            search: { hidden: true }
          },
          value8: {
            type: 'text',
            label: '日志内容',
            showOverflowTooltip: true,
            search: { hidden: true }
          }
        }
      }
    }
  },
  methods: {
    // 判断是否为视频文件
    isVideo(url) {
      if (!url) return false
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv']
      return videoExtensions.some(ext => url.toLowerCase().includes(ext))
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '运行中': 'primary',
        '已完成': 'success',
        '已停止': 'info',
        '待执行': 'warning',
        '异常': 'danger',
        '失败': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 获取进度条状态
    getProgressStatus(status) {
      if (status === '异常' || status === '失败') return 'exception'
      if (status === '已完成') return 'success'
      return null
    },

    // 截断日志内容
    truncateLog(log, maxLength = 50) {
      if (!log) return ''
      return log.length > maxLength ? log.substring(0, maxLength) + '...' : log
    },

    // 刷新数据
    refresh() {
      this.$refs.sheetRef?.getTableData()
    }
  }
}
</script>

<style scoped>
.operator-monitor {
  margin-top: 20px;
}

.monitor-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.monitor-sheet {
}

.media-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-video,
.preview-image {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.log-content {
  max-width: 200px;
}

.log-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.text-gray-400 {
  color: #909399;
}
</style>
