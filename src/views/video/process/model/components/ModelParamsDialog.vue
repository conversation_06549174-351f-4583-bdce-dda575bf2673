<template>
  <el-dialog
    title="模型参数配置"
    :visible.sync="visible"
    width="800px"
    append-to-body
    @closed="onClosed"
  >
    <div class="model-params-container">
      <!-- 模型信息 -->
      <div v-if="modelData" class="model-info">
        <div class="info-header">
          <div class="model-icon">
            <i :class="getAlgorithmIcon(modelData.value3)" />
          </div>
          <div class="model-details">
            <h3>{{ modelData.value2 }}</h3>
            <p>{{ modelData.value1 }} - {{ getTypeLabel(modelData.value3) }}</p>
          </div>
        </div>
      </div>

      <!-- 参数样例 -->
      <div class="param-example-section">
        <div class="section-header">
          <h4>参数样例</h4>
          <el-tooltip content="用于展示算法调用时的参数示例" placement="top">
            <i class="el-icon-question" style="color: #909399; margin-left: 4px;"></i>
          </el-tooltip>
        </div>
        <div class="example-editor">
          <el-input
            v-model="paramExample"
            type="textarea"
            :rows="8"
            placeholder="请输入参数样例（JSON格式）&#10;例如：&#10;{&#10;  &quot;url&quot;: &quot;http://example.com/video.mp4&quot;,&#10;  &quot;quality&quot;: &quot;high&quot;,&#10;  &quot;format&quot;: &quot;mp4&quot;&#10;}"
          />
          <!-- @blur="validateParamExample" -->
          <div v-if="exampleError" class="error-tip">
            <i class="el-icon-warning"></i>
            {{ exampleError }}
          </div>
        </div>
      </div>

      <!-- 参数配置 -->
      <div class="params-section">
        <div class="section-header">
          <h4>参数配置</h4>
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="addParam">
            添加参数
          </el-button>
        </div>

        <div v-if="paramsList.length === 0" class="empty-params">
          <el-empty description="暂无参数配置">
            <!-- <el-button type="primary" @click="addParam">添加第一个参数</el-button> -->
          </el-empty>
        </div>

        <el-form v-else ref="paramsForm" :model="{ paramsList }" label-width="100px">
          <div class="params-list">
            <div
              v-for="(param, index) in paramsList"
              :key="param.id || index"
              class="param-item"
            >
              <div class="param-form">
                <el-row :gutter="16">
                  <el-col :span="6">
                    <el-form-item
                      label="参数名称"
                      :prop="`paramsList.${index}.name`"
                      :rules="[
                        { required: true, message: '请输入参数名称', trigger: 'blur' }
                      ]"
                    >
                      <el-input
                        v-model="param.name"
                        placeholder="请输入参数名称"
                        @blur="validateParamName(param, index)"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      label="参数键"
                      :prop="`paramsList.${index}.key`"
                      :rules="[
                        { required: true, message: '请输入参数键', trigger: 'blur' }
                      ]"
                    >
                      <el-input
                        v-model="param.key"
                        placeholder="请输入参数键"
                        @blur="validateParamKey(param, index)"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item
                      label="参数类型"
                      :prop="`paramsList.${index}.type`"
                      :rules="[
                        { required: true, message: '请选择参数类型', trigger: 'change' }
                      ]"
                    >
                      <el-select v-model="param.type" placeholder="请选择类型">
                        <el-option label="字符串" value="string" />
                        <el-option label="数字" value="number" />
                        <el-option label="布尔值" value="boolean" />
                        <el-option label="选择器" value="select" />
                        <el-option label="文件路径" value="file" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="4">
                    <el-form-item label="必填">
                      <el-switch v-model="param.required" />
                    </el-form-item>
                  </el-col> -->
                  <el-col :span="2">
                    <el-form-item label=" ">
                      <el-button
                        type="danger"
                        size="mini"
                        icon="el-icon-delete"
                        @click="removeParam(index)"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- <el-row :gutter="16">
                  <el-col :span="8">
                    <el-form-item label="默认值">
                      <el-input
                        v-model="param.defaultValue"
                        placeholder="请输入默认值"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="16">
                    <el-form-item label="参数描述">
                      <el-input
                        v-model="param.description"
                        placeholder="请输入参数描述"
                      />
                    </el-form-item>
                  </el-col>
                </el-row> -->

                <!-- 选择器类型的选项配置 -->
                <div v-if="param.type === 'select'" class="select-options">
                  <el-form-item label="选项配置">
                    <div class="options-list">
                      <div
                        v-for="(option, optIndex) in param.options || []"
                        :key="optIndex"
                        class="option-item"
                      >
                        <el-input
                          v-model="option.label"
                          placeholder="选项标签"
                          style="width: 45%"
                        />
                        <el-input
                          v-model="option.value"
                          placeholder="选项值"
                          style="width: 45%; margin-left: 8px"
                        />
                        <el-button
                          type="danger"
                          size="mini"
                          icon="el-icon-delete"
                          style="margin-left: 8px"
                          @click="removeOption(param, optIndex)"
                        />
                      </div>
                      <el-button
                        type="text"
                        icon="el-icon-plus"
                        @click="addOption(param)"
                      >
                        添加选项
                      </el-button>
                    </div>
                  </el-form-item>
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">保存配置</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { algorithmType } from '@/dicts/video/index.js'
import request from '@/utils/request.js'

export default {
  name: 'ModelParamsDialog',
  data() {
    return {
      visible: false,
      modelData: null,
      paramsList: [],
      paramExample: '', // 参数样例
      exampleError: '', // 样例验证错误信息
      algorithmTypeDict: algorithmType
    }
  },
  methods: {
    async open(modelData) {
      this.visible = true
      this.modelData = modelData
      await this.loadParams()
    },

    close() {
      this.visible = false
    },

    onClosed() {
      this.modelData = null
      this.paramsList = []
      this.paramExample = ''
      this.exampleError = ''
      // 清除表单验证
      if (this.$refs.paramsForm) {
        this.$refs.paramsForm.clearValidate()
      }
    },

    // 加载参数配置
    async loadParams() {
      if (!this.modelData) return

      try {
        // 加载参数配置
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'model_params_config',
            value1: this.modelData.id, // 模型ID
            pageSize: 100
          }
        })

        if (response.code === 200 && response.rows) {
          this.paramsList = response.rows.map(item => {
            try {
              return JSON.parse(item.value2) // 参数配置存储在value2中
            } catch (e) {
              return this.createDefaultParam()
            }
          })
        }

        // 如果没有参数配置，添加一个默认参数
        if (this.paramsList.length === 0) {
          this.addParam()
        }

        // 加载参数样例
        await this.loadParamExample()
      } catch (error) {
        console.error('加载参数配置失败:', error)
        this.$message.error('加载参数配置失败')
      }
    },

    // 加载参数样例
    async loadParamExample() {
      if (!this.modelData) return

      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'model_param_example',
            value1: this.modelData.id, // 模型ID
            pageSize: 1
          }
        })

        if (response.code === 200 && response.rows && response.rows.length > 0) {
          this.paramExample = response.rows[0].value2 || ''
        }
      } catch (error) {
        console.error('加载参数样例失败:', error)
        // 不显示错误信息，因为参数样例是可选的
      }
    },

    // 创建默认参数
    createDefaultParam() {
      return {
        id: Date.now() + Math.random(),
        name: '',
        key: '',
        type: 'string',
        required: false,
        defaultValue: '',
        description: '',
        options: []
      }
    },

    // 添加参数
    addParam() {
      this.paramsList.push(this.createDefaultParam())
    },

    // 删除参数
    removeParam(index) {
      this.$confirm('确认删除此参数吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.paramsList.splice(index, 1)
      }).catch(() => {})
    },

    // 添加选项
    addOption(param) {
      if (!param.options) {
        this.$set(param, 'options', [])
      }
      param.options.push({ label: '', value: '' })
    },

    // 删除选项
    removeOption(param, index) {
      param.options.splice(index, 1)
    },

    // 验证参数名称
    validateParamName(param, index) {
      if (!param.name.trim()) return

      // 检查重复
      const duplicateIndex = this.paramsList.findIndex((p, i) =>
        i !== index && p.name === param.name
      )
      if (duplicateIndex !== -1) {
        this.$message.warning('参数名称不能重复')
        param.name = ''
      }
    },

    // 验证参数键
    validateParamKey(param, index) {
      if (!param.key.trim()) return

      // 检查重复
      const duplicateIndex = this.paramsList.findIndex((p, i) =>
        i !== index && p.key === param.key
      )
      if (duplicateIndex !== -1) {
        this.$message.warning('参数键不能重复')
        param.key = ''
      }

      // 验证格式（只允许字母、数字、下划线）
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(param.key)) {
        this.$message.warning('参数键只能包含字母、数字、下划线，且不能以数字开头')
        param.key = ''
      }
    },

    // 验证参数样例
    validateParamExample() {
      this.exampleError = ''

      if (!this.paramExample.trim()) {
        return // 允许为空
      }

      try {
        JSON.parse(this.paramExample)
      } catch (error) {
        this.exampleError = 'JSON格式不正确，请检查语法'
      }
    },

    // 获取算法图标
    getAlgorithmIcon(type) {
      const iconMap = {
        'encode_convert': 'el-icon-refresh',
        'data_preprocess': 'el-icon-magic-stick',
        'deep_process': 'el-icon-cpu'
      }
      return iconMap[type] || 'el-icon-coin'
    },

    // 保存配置
    async confirm() {
      // 验证参数样例格式
      // if (this.paramExample.trim()) {
      //   this.validateParamExample()
      //   if (this.exampleError) {
      //     this.$message.error('参数样例格式不正确')
      //     return
      //   }
      // }

      // 如果有参数，先进行表单验证
      if (this.paramsList.length > 0) {
        try {
          await this.$refs.paramsForm.validate()
        } catch (error) {
          this.$message.error('请完善参数配置信息')
          return
        }
      }

      // 验证必填项
      const invalidParams = this.paramsList.filter(param =>
        !param.name.trim() || !param.key.trim() || !param.type
      )

      if (invalidParams.length > 0) {
        this.$message.error('请完善所有参数的名称、键和类型')
        return
      }

      try {
        // 先删除旧的参数配置和样例
        await this.clearOldParams()
        await this.clearOldParamExample()

        // 保存新的参数配置
        for (const param of this.paramsList) {
          await request({
            url: '/system/AutoOsmotic',
            method: 'post',
            data: {
              type: 'model_params_config',
              value1: this.modelData.id, // 模型ID
              value2: JSON.stringify(param), // 参数配置
              value3: param.name, // 参数名称（便于查询）
              value4: param.key, // 参数键（便于查询）
              value5: param.type // 参数类型（便于查询）
            }
          })
        }

        // 保存参数样例
        if (this.paramExample.trim()) {
          await request({
            url: '/system/AutoOsmotic',
            method: 'post',
            data: {
              type: 'model_param_example',
              value1: this.modelData.id, // 模型ID
              value2: this.paramExample, // 参数样例
              value3: '参数样例' // 描述
            }
          })
        }

        // 更新模型的参数量
        await request({
          url: '/system/AutoOsmotic',
          method: 'put',
          data: {
            ...this.modelData,
            value4: this.paramsList.length.toString()
          }
        })

        this.$message.success('参数配置保存成功')
        this.$emit('success')
        this.close()
      } catch (error) {
        console.error('保存参数配置失败:', error)
        this.$message.error('保存参数配置失败')
      }
    },

    // 清除旧的参数配置
    async clearOldParams() {
      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'model_params_config',
            value1: this.modelData.id,
            pageSize: 100
          }
        })

        if (response.code === 200 && response.rows) {
          for (const item of response.rows) {
            await request({
              url: `/system/AutoOsmotic/${item.id}`,
              method: 'delete'
            })
          }
        }
      } catch (error) {
        console.error('清除旧参数配置失败:', error)
      }
    },

    // 清除旧的参数样例
    async clearOldParamExample() {
      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'model_param_example',
            value1: this.modelData.id,
            pageSize: 100
          }
        })

        if (response.code === 200 && response.rows) {
          for (const item of response.rows) {
            await request({
              url: `/system/AutoOsmotic/${item.id}`,
              method: 'delete'
            })
          }
        }
      } catch (error) {
        console.error('清除旧参数样例失败:', error)
      }
    },

    // 获取类型标签
    getTypeLabel(type) {
      const typeItem = this.algorithmTypeDict.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    }
  }
}
</script>

<style lang="scss" scoped>
.model-params-container {
  .model-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .info-header {
      display: flex;
      align-items: center;

      .model-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        i {
          font-size: 24px;
          color: white;
        }
      }

      .model-details {
        h3 {
          margin: 0 0 4px 0;
          font-size: 18px;
          color: #303133;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .param-example-section {
    margin-bottom: 24px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }
    }

    .example-editor {
      .error-tip {
        margin-top: 8px;
        padding: 8px 12px;
        background: #fef0f0;
        border: 1px solid #fbc4c4;
        border-radius: 4px;
        color: #f56c6c;
        font-size: 12px;
        display: flex;
        align-items: center;

        i {
          margin-right: 4px;
        }
      }
    }
  }

  .params-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }
    }

    .empty-params {
      text-align: center;
      padding: 40px 0;
    }

    .params-list {
      .param-item {
        margin-bottom: 24px;
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        background: #fff;

        &:hover {
          border-color: #c6e2ff;
          background: #f5f9ff;
        }

        .param-form {
          .el-form-item {
            margin-bottom: 16px;
          }
        }

        .select-options {
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;

          .options-list {
            .option-item {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
