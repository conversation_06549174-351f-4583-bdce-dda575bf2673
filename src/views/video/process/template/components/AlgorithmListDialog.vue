<template>
  <el-dialog
    title="算法执行列表"
    :visible.sync="visible"
    width="800px"
    append-to-body
    @closed="onClosed"
  >
    <div class="algorithm-list-container">
      <!-- 工具栏 -->
      <div class="toolbar">
        <el-button type="primary" icon="el-icon-plus" @click="handleAddModel">
          添加算法
        </el-button>
        <div class="toolbar-right">
          <span class="count-info">共 {{ algorithmList.length }} 个算法</span>
        </div>
      </div>

      <!-- 算法列表 -->
      <div class="algorithm-list">
        <div v-if="algorithmList.length === 0" class="empty-container">
          <el-empty description="暂无算法，请点击添加算法">
            <!-- <el-button type="primary" @click="handleAddModel">添加算法</el-button> -->
          </el-empty>
        </div>
        <div v-else class="list-content">
          <draggable
            v-model="algorithmList"
            handle=".drag-handle"
            @end="handleDragEnd"
          >
            <div
              v-for="(item, index) in algorithmList"
              :key="item.id"
              class="algorithm-item"
            >
              <!-- 序号和拖拽手柄 -->
              <div class="item-index">
                <span class="index-number">{{ index + 1 }}</span>
                <i class="el-icon-s-operation drag-handle" title="拖拽排序"></i>
              </div>

              <!-- 算法信息 -->
              <div class="item-content">
                <div class="algorithm-info">
                  <div class="algorithm-icon">
                    <i :class="getAlgorithmIcon(item.type)" />
                  </div>
                  <div class="algorithm-details">
                    <h4 class="algorithm-name">{{ item.name }}</h4>
                    <p class="algorithm-code">{{ item.code }}</p>
                    <div class="algorithm-meta">
                      <el-tag size="mini" :type="getTypeTagType(item.type)">
                        {{ getTypeLabel(item.type) }}
                      </el-tag>
                      <span class="meta-item">版本: {{ item.version || '1.0.0' }}</span>
                      <span class="meta-item">参数: {{ item.paramCount || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="item-actions">
                <el-button-group>
                  <el-button
                    size="mini"
                    icon="el-icon-top"
                    :disabled="index === 0"
                    title="上移"
                    @click="moveUp(index)"
                  />
                  <el-button
                    size="mini"
                    icon="el-icon-bottom"
                    :disabled="index === algorithmList.length - 1"
                    title="下移"
                    @click="moveDown(index)"
                  />
                </el-button-group>
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-setting"
                  @click="handleConfig(item)"
                >
                  配置
                </el-button>
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleRemove(index)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </draggable>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>

    <!-- 模型选择对话框 -->
    <ModelSelectDialog
      ref="modelSelectDialog"
      @success="handleModelSelected"
    />

    <!-- 模型配置对话框 -->
    <ModelConfigDialog
      ref="modelConfigDialog"
    />
  </el-dialog>
</template>

<script>
import draggable from 'vuedraggable'
import { algorithmType } from '@/dicts/video/index.js'
import ModelSelectDialog from './ModelSelectDialog.vue'
import ModelConfigDialog from './ModelConfigDialog.vue'

export default {
  name: 'AlgorithmListDialog',
  components: {
    draggable,
    ModelSelectDialog,
    ModelConfigDialog
  },
  data() {
    return {
      visible: false,
      algorithmList: [],
      algorithmTypeDict: algorithmType,
      success: null
    }
  },
  methods: {
    open(args = {}) {
      this.visible = true
      this.success = args.success || this.success
      this.algorithmList = args.algorithms ? [...args.algorithms] : []
    },

    close() {
      this.visible = false
    },

    onClosed() {
      this.algorithmList = []
      this.success = null
    },

    confirm() {
      if (this.success) {
        this.success([...this.algorithmList])
      }
      this.close()
    },

    // 添加算法模型
    handleAddModel() {
      this.$refs.modelSelectDialog.open({
        selected: this.algorithmList,
        success: this.handleModelSelected
      })
    },

    // 处理模型选择结果
    handleModelSelected(selectedModels) {
      // 过滤掉已存在的模型
      const existingIds = this.algorithmList.map(item => item.id)
      const newModels = selectedModels.filter(model => !existingIds.includes(model.id))

      // 添加到列表末尾
      this.algorithmList.push(...newModels)

      if (newModels.length > 0) {
        this.$message.success(`成功添加 ${newModels.length} 个算法`)
      }
    },

    // 上移
    moveUp(index) {
      if (index > 0) {
        const item = this.algorithmList.splice(index, 1)[0]
        this.algorithmList.splice(index - 1, 0, item)
      }
    },

    // 下移
    moveDown(index) {
      if (index < this.algorithmList.length - 1) {
        const item = this.algorithmList.splice(index, 1)[0]
        this.algorithmList.splice(index + 1, 0, item)
      }
    },

    // 删除
    handleRemove(index) {
      const item = this.algorithmList[index]
      this.$confirm(`确认要删除当前算法吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.algorithmList.splice(index, 1)
        this.$message.success('删除成功')
      }).catch(() => {})
    },

    // 查看配置
    handleConfig(item) {
      this.$refs.modelConfigDialog.open(item)
    },

    // 拖拽结束
    handleDragEnd() {
      // 拖拽排序已由 v-model 自动处理
    },

    // 获取算法图标
    getAlgorithmIcon(type) {
      const iconMap = {
        'encode_convert': 'el-icon-refresh',
        'data_preprocess': 'el-icon-magic-stick',
        'deep_process': 'el-icon-cpu'
      }
      return iconMap[type] || 'el-icon-coin'
    },

    // 获取类型标签
    getTypeLabel(type) {
      const typeItem = this.algorithmTypeDict.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },

    // 获取类型标签颜色
    getTypeTagType(type) {
      const typeMap = {
        'encode_convert': 'primary',
        'data_preprocess': 'success',
        'deep_process': 'warning'
      }
      return typeMap[type] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.algorithm-list-container {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;

    .toolbar-right {
      .count-info {
        font-size: 13px;
        color: #909399;
      }
    }
  }

  .algorithm-list {
    max-height: 500px;
    overflow-y: auto;

    .empty-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
    }

    .list-content {
      .algorithm-item {
        display: flex;
        align-items: center;
        padding: 16px;
        margin-bottom: 12px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        background: #fff;
        transition: all 0.3s ease;

        &:hover {
          border-color: #c6e2ff;
          background: #f5f9ff;
        }

        .item-index {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-right: 16px;
          min-width: 40px;

          .index-number {
            font-size: 16px;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 4px;
          }

          .drag-handle {
            font-size: 14px;
            color: #c0c4cc;
            cursor: move;

            &:hover {
              color: #409eff;
            }
          }
        }

        .item-content {
          flex: 1;
          min-width: 0;

          .algorithm-info {
            display: flex;
            align-items: center;

            .algorithm-icon {
              width: 48px;
              height: 48px;
              border-radius: 8px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 16px;
              flex-shrink: 0;

              i {
                font-size: 24px;
                color: white;
              }
            }

            .algorithm-details {
              flex: 1;
              min-width: 0;

              .algorithm-name {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                margin: 0 0 4px 0;
                line-height: 1.4;
              }

              .algorithm-code {
                font-size: 13px;
                color: #909399;
                margin: 0 0 8px 0;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              }

              .algorithm-meta {
                display: flex;
                align-items: center;
                gap: 12px;

                .meta-item {
                  font-size: 12px;
                  color: #606266;
                }
              }
            }
          }
        }

        .item-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
