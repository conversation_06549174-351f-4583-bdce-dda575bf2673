<template>
  <div>
    <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
      <template #before></template>

      <!-- 工具栏自定义按钮 -->
      <template #toolbar:after>
        <!-- 无额外工具栏按钮 -->
      </template>

      <!-- 索引状态列自定义渲染 -->
      <template #table:value6:simple="{ row }">
        <el-tag :type="getIndexStatusTagType(row.value6)">
          {{ row.value6 }}
        </el-tag>
      </template>

      <!-- 操作列自定义渲染 -->
      <template #table:action:after="{ row }">
        <el-button type="text" size="mini" @click="handleFrameView(row)">帧向量明细</el-button>
      </template>

      <template #info:before></template>
      <template #after></template>
    </EleSheet>

    <!-- 帧向量明细弹窗组件 -->
    <FrameVectorDetailDialog
      ref="frameDetailDialogRef"
      @closed="handleFrameDetailClosed"
    />
  </div>
</template>

<script>
import request from '@/utils/request.js'
import { indexStatus } from '@/dicts/video/index.js'
import FrameVectorDetailDialog from './components/FrameVectorDetailDialog.vue'

export default {
  name: 'VideoVectorManagement',
  components: {
    FrameVectorDetailDialog
  },
  data() {
    return {
      tableType: 'vector_storage_management'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频向量数据',
        tableActionProps: {
          width: 250
        },
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          // add: (data) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'post',
          //     data: {
          //       ...data,
          //       type: this.tableType
          //     }
          //   }),
          // edit: (data) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'put',
          //     data: {
          //       ...data,
          //       type: this.tableType
          //     }
          //   }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 视频向量存储表
          value1: {
            type: 'text',
            label: '视频向量存储表',
            align: 'left',
            width: 150,
            search: {
              placeholder: '请输入存储表名称'
            }
          },
          // 视频唯一编号
          value2: {
            type: 'text',
            label: '视频唯一编号',
            align: 'left',
            width: 180,
            search: {
              placeholder: '请输入视频编号'
            }
          },
          // 帧向量总量
          value3: {
            type: 'text',
            label: '帧向量总量',
            align: 'right',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 单帧向量维度
          value4: {
            type: 'text',
            label: '单帧向量维度',
            align: 'right',
            width: 130,
            search: {
              hidden: true
            }
          },
          // 视频识别结果
          value5: {
            type: 'text',
            label: '视频识别结果',
            align: 'left',
            width: 200,
            search: {
              hidden: true
            }
          },
          // 索引状态
          value6: {
            type: 'select',
            label: '索引状态',
            width: 100,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...indexStatus
              ]
            },
            options: indexStatus
          },
          // 存储容量
          value7: {
            type: 'text',
            label: '存储容量',
            align: 'right',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 最近更新时间
          value8: {
            type: 'datetime',
            label: '最近更新时间',
            width: 160,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          }
        }
      }
    }
  },
  methods: {
    // 获取索引状态标签类型
    getIndexStatusTagType(status) {
      const statusMap = {
        '正常': 'success',
        '需优化': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 查看帧向量明细
    handleFrameView(row) {
      this.$refs.frameDetailDialogRef.open(row)
    },

    // 帧向量明细弹窗关闭事件
    handleFrameDetailClosed() {
      // 弹窗关闭后的处理逻辑，如果需要的话
      console.log('帧向量明细弹窗已关闭')
    },

    // 查看操作
    handleView(row) {
      this.$modal.msgInfo(`查看向量数据详情：视频编号 ${row.value2}`)
      // 这里可以打开详情弹窗或跳转到详情页面
      console.log('查看向量数据:', row)
    },

    // 刷新操作
    handleRefresh(row) {
      this.$modal.confirm(`确认要刷新吗？`).then(() => {
        // 这里可以调用刷新接口
        console.log('刷新向量数据:', row)
        this.$modal.msgSuccess('刷新成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 下载操作
    handleDownload(row) {
      this.$modal.confirm(`确认要下载吗？`).then(() => {
        // 这里可以调用下载接口
        console.log('下载向量数据:', row)
        this.$modal.msgSuccess('下载任务已启动')
      }).catch(() => {})
    },

    // 删除操作
    handleDelete(row) {
      this.$modal.confirm(`确认要删除吗？此操作不可恢复！`).then(() => {
        // 这里可以调用删除接口
        console.log('删除向量数据:', row)
        this.$modal.msgSuccess('删除成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
